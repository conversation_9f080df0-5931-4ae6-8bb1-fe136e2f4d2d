import { useState, useEffect, useRef } from 'react'
import styles from '@/styles/admin/POS.module.css'

/**
 * POSSquarePayment component for processing Square card payments in POS
 *
 * @param {Object} props - Component props
 * @param {number} props.amount - Amount to charge
 * @param {string} props.currency - Currency code (default: AUD)
 * @param {Function} props.onSuccess - Function to call on successful payment
 * @param {Function} props.onError - Function to call on payment error
 * @param {Object} props.orderDetails - Order details for Square
 * @returns {JSX.Element}
 */
export default function POSSquarePayment({
  amount,
  currency = 'AUD',
  onSuccess,
  onError,
  orderDetails = {}
}) {
  const [paymentForm, setPaymentForm] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [initializationAttempted, setInitializationAttempted] = useState(false)
  const [billingAddress, setBillingAddress] = useState({
    addressLine1: '1455 Market St',
    addressLine2: 'Suite 600',
    locality: 'San Francisco',
    administrativeDistrictLevel1: 'CA',
    postalCode: '94103',
    country: 'US'
  })
  const [showBillingAddress, setShowBillingAddress] = useState(false)
  const containerRef = useRef(null)
  const mountedRef = useRef(false)
  const paymentFormRef = useRef(null) // Track payment form for cleanup

  // Safe DOM cleanup function to prevent React DOM errors
  const safeCleanupSquareForm = () => {
    console.log('🧹 Starting safe Square form cleanup...')

    try {
      // Step 1: Destroy Square payment form first (most important)
      const currentForm = paymentFormRef.current || paymentForm
      if (currentForm) {
        try {
          console.log('Destroying Square payment form...')
          currentForm.destroy()
          console.log('✅ Square payment form destroyed successfully')
        } catch (destroyError) {
          console.warn('⚠️ Error destroying Square payment form:', destroyError)
        }
        paymentFormRef.current = null
      }

      // Step 2: Wait for Square SDK to clean up its DOM elements
      setTimeout(() => {
        try {
          // Step 3: Safe container cleanup with defensive checks
          const container = containerRef.current || document.getElementById('pos-square-card-container')
          if (container && container.parentNode) {
            console.log('Cleaning container content safely...')

            // Special handling for Square SDK iframes and elements
            const squareElements = container.querySelectorAll('iframe, [id*="sq-"], [class*="sq-"]')
            if (squareElements.length > 0) {
              console.log(`Found ${squareElements.length} Square SDK elements to clean up`)
              squareElements.forEach((element, index) => {
                try {
                  if (element.parentNode) {
                    element.parentNode.removeChild(element)
                    console.log(`✅ Removed Square element ${index + 1}/${squareElements.length}`)
                  }
                } catch (squareRemoveError) {
                  console.warn(`⚠️ Could not remove Square element ${index + 1}:`, squareRemoveError)
                }
              })
            }

            // Check if container has children before attempting removal
            const children = Array.from(container.children)
            if (children.length > 0) {
              console.log(`Removing ${children.length} remaining child elements`)
              children.forEach((child, index) => {
                try {
                  if (child.parentNode === container) {
                    container.removeChild(child)
                    console.log(`✅ Removed child ${index + 1}/${children.length}`)
                  }
                } catch (removeError) {
                  console.warn(`⚠️ Could not remove child ${index + 1}:`, removeError)
                  // Try alternative cleanup method
                  try {
                    child.remove()
                  } catch (altRemoveError) {
                    console.warn('⚠️ Alternative removal also failed:', altRemoveError)
                  }
                }
              })
            }

            // Final cleanup - only if container is still valid and has content
            if (container.parentNode && container.innerHTML.trim() !== '') {
              try {
                container.innerHTML = ''
                console.log('✅ Container innerHTML cleared')
              } catch (innerHTMLError) {
                console.warn('⚠️ Could not clear innerHTML:', innerHTMLError)
              }
            }
          }
        } catch (containerError) {
          console.warn('⚠️ Container cleanup error:', containerError)
        }
      }, 150) // Give Square SDK more time to clean up its iframe elements

    } catch (overallError) {
      console.error('❌ Overall cleanup error:', overallError)
    }
  }

  // Production-optimized React hydration and container initialization
  useEffect(() => {
    mountedRef.current = true
    console.log('POSSquarePayment component mounted')

    let retryCount = 0
    const maxRetries = 30 // Reduced to 3 seconds (30 * 100ms) for faster failure detection
    let initializationStarted = false

    // Enhanced container detection that works in production builds
    const checkContainerAvailability = () => {
      // Strategy 1: Direct DOM query (most reliable in production)
      const domElement = document.getElementById('pos-square-card-container')

      // Strategy 2: React ref (backup method)
      const refElement = containerRef.current

      // Strategy 3: Query selector with class (additional fallback)
      const classElement = document.querySelector('[data-testid="square-card-container"]')

      const containerFound = domElement || refElement || classElement

      if (containerFound) {
        console.log('Container found via:', {
          domQuery: !!domElement,
          reactRef: !!refElement,
          testId: !!classElement,
          element: containerFound
        })
        return containerFound
      }

      return null
    }

    // Wait for React hydration and DOM to be ready before initializing Square SDK
    const initializeAfterHydration = () => {
      if (!mountedRef.current) {
        console.log('Component unmounted during initialization, stopping')
        return
      }

      // Check retry limit to prevent infinite loops
      if (retryCount >= maxRetries) {
        console.error(`Container failed to initialize after ${maxRetries} retries (${maxRetries * 100}ms). This indicates a React hydration issue.`)
        setIsLoading(false)
        setErrorMessage('Payment system initialization failed. This may be due to a browser compatibility issue. Please refresh the page and try again.')
        return
      }

      // Check if container is available using multiple strategies
      const container = checkContainerAvailability()
      if (!container) {
        retryCount++
        console.log(`Container not ready, retrying in 100ms... (attempt ${retryCount}/${maxRetries})`)
        setTimeout(initializeAfterHydration, 100)
        return
      }

      // Prevent multiple initialization attempts
      if (initializationStarted) {
        console.log('Initialization already started, skipping duplicate attempt')
        return
      }

      initializationStarted = true
      console.log('React hydration complete, container ready, loading Square SDK...')
      loadSquareSDK()
    }

    // Production-optimized timing strategy
    if (typeof window !== 'undefined') {
      // For production builds, use longer initial delay to ensure hydration is complete
      const isProduction = process.env.NODE_ENV === 'production'
      const initialDelay = isProduction ? 200 : 50

      console.log(`Using ${isProduction ? 'production' : 'development'} timing strategy with ${initialDelay}ms initial delay`)

      // Strategy 1: Wait for next tick to ensure React has finished rendering
      setTimeout(() => {
        // Strategy 2: Use requestAnimationFrame to wait for browser paint
        requestAnimationFrame(() => {
          // Strategy 3: Additional delay for React hydration (longer in production)
          setTimeout(initializeAfterHydration, initialDelay)
        })
      }, 0)
    }

    return () => {
      mountedRef.current = false
      initializationStarted = false

      console.log('POSSquarePayment component unmounting - starting safe cleanup')
      safeCleanupSquareForm()
    }
  }, [])

  // Cleanup payment form when paymentForm changes (but not on unmount - handled above)
  useEffect(() => {
    // Store current form in ref for cleanup access
    paymentFormRef.current = paymentForm

    return () => {
      // Only clean up if we're changing forms, not unmounting
      if (mountedRef.current && paymentForm) {
        console.log('Payment form changing - performing safe cleanup')
        safeCleanupSquareForm()
      }
    }
  }, [paymentForm])

  // Listen for force initialization events from debugger
  useEffect(() => {
    const handleForceInit = (event) => {
      console.log('🔄 Force initialization triggered by:', event.detail?.source)
      if (mountedRef.current && window.Square) {
        // Reset initialization state and try again
        setInitializationAttempted(false)
        setIsLoading(true)
        setErrorMessage('')

        // Small delay to ensure state updates
        setTimeout(() => {
          loadSquareSDK()
        }, 100)
      } else {
        console.warn('Cannot force init: mounted =', mountedRef.current, 'Square =', !!window.Square)
      }
    }

    window.addEventListener('forceSquareInit', handleForceInit)

    return () => {
      window.removeEventListener('forceSquareInit', handleForceInit)
    }
  }, [])

  const loadSquareSDK = () => {
    // Prevent multiple initialization attempts
    if (initializationAttempted) {
      console.log('Square SDK initialization already attempted')
      return
    }

    setInitializationAttempted(true)

    // Check if Square SDK is already loaded
    if (window.Square) {
      console.log('Square SDK already loaded, initializing payment form...')
      // Use requestAnimationFrame for better timing
      requestAnimationFrame(() => {
        if (mountedRef.current) {
          initializePaymentForm()
        }
      })
      return
    }

    console.log('Loading Square SDK...')

    // Load Square Web Payments SDK
    const script = document.createElement('script')
    script.src = 'https://sandbox.web.squarecdn.com/v1/square.js'
    script.async = true
    script.onload = () => {
      console.log('Square SDK loaded successfully')
      // Use requestAnimationFrame for better timing
      requestAnimationFrame(() => {
        if (mountedRef.current) {
          initializePaymentForm()
        }
      })
    }
    script.onerror = (error) => {
      console.error('Failed to load Square SDK:', error)
      if (mountedRef.current) {
        setIsLoading(false)
        setErrorMessage('Failed to load Square payment system. Please check your internet connection.')
        onError(new Error('Square SDK failed to load'))
      }
    }

    // Check if script already exists to avoid duplicates
    const existingScript = document.querySelector('script[src="https://sandbox.web.squarecdn.com/v1/square.js"]')
    if (!existingScript) {
      document.head.appendChild(script)
    } else {
      console.log('Square SDK script already exists, waiting for load...')
      // If script exists but Square isn't loaded yet, wait for it
      let attempts = 0
      const maxAttempts = 100 // 10 seconds

      const checkSquare = () => {
        attempts++

        // Check if component is still mounted before continuing
        if (!mountedRef.current) {
          console.log('Component unmounted, stopping Square SDK detection')
          return
        }

        if (window.Square) {
          console.log(`Square SDK detected after ${attempts} attempts`)
          if (mountedRef.current) {
            requestAnimationFrame(() => initializePaymentForm())
          }
          return
        }

        if (attempts >= maxAttempts) {
          console.error('Square SDK failed to load within timeout period')
          if (mountedRef.current) {
            setIsLoading(false)
            setErrorMessage('Square SDK failed to load within timeout period.')
            onError(new Error('Square SDK load timeout'))
          }
          return
        }

        // Continue checking only if component is still mounted
        if (mountedRef.current) {
          setTimeout(checkSquare, 100)
        }
      }

      checkSquare()
    }
  }

  const initializePaymentForm = async () => {
    console.log('initializePaymentForm called')

    // Check if component is still mounted using ref instead of state
    if (!mountedRef.current) {
      console.log('Component unmounted, skipping initialization')
      return
    }

    // Prevent multiple initialization if payment form already exists
    if (paymentForm) {
      console.log('Payment form already initialized, skipping duplicate initialization')
      return
    }

    if (!window.Square) {
      console.error('Square SDK not available')
      if (mountedRef.current) {
        setErrorMessage('Square payment system not available')
        setIsLoading(false)
      }
      return
    }

    try {
      // Enhanced container detection using multiple strategies
      console.log('Looking for element: #pos-square-card-container')

      // Strategy 1: Direct DOM query (most reliable in production)
      let containerElement = document.getElementById('pos-square-card-container')
      let detectionMethod = 'DOM query'

      // Strategy 2: React ref (backup method)
      if (!containerElement && containerRef.current) {
        containerElement = containerRef.current
        detectionMethod = 'React ref'
      }

      // Strategy 3: Query selector with test ID (additional fallback)
      if (!containerElement) {
        containerElement = document.querySelector('[data-testid="square-card-container"]')
        detectionMethod = 'test ID query'
      }

      // Strategy 4: Query selector with class (final fallback)
      if (!containerElement) {
        containerElement = document.querySelector('.cardForm')
        detectionMethod = 'class query'
      }

      if (!containerElement) {
        // Production fallback: Try to create the container if it doesn't exist
        console.warn('Container not found, attempting to create fallback container...')

        const fallbackContainer = document.createElement('div')
        fallbackContainer.id = 'pos-square-card-container'
        fallbackContainer.className = 'cardForm'
        fallbackContainer.setAttribute('data-testid', 'square-card-container')

        // Try to find a parent container to append to
        const parentContainer = document.querySelector('.cardFormContainer') ||
                               document.querySelector('.squarePaymentContainer') ||
                               document.body

        if (parentContainer) {
          parentContainer.appendChild(fallbackContainer)
          containerElement = fallbackContainer
          detectionMethod = 'fallback creation'
          console.log('Fallback container created and appended to:', parentContainer)
        } else {
          throw new Error('Square payment container not available and cannot create fallback. DOM structure may be corrupted.')
        }
      }

      console.log(`Element found via ${detectionMethod}:`, containerElement)
      console.log('Container element found:', containerElement)

      // Validate environment variables
      const appId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID
      const locationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID

      if (!appId || !locationId) {
        throw new Error(`Missing Square configuration: appId=${!!appId}, locationId=${!!locationId}`)
      }

      console.log('Initializing Square payments with:', { appId, locationId })

      // Initialize Square payments
      const payments = window.Square.payments(appId, locationId)

      // Determine if we're in sandbox environment
      const isSandbox = process.env.NODE_ENV !== 'production' ||
                       process.env.SQUARE_ENVIRONMENT === 'sandbox'

      console.log('Square environment configuration:', {
        environment: isSandbox ? 'sandbox' : 'production',
        nodeEnv: process.env.NODE_ENV,
        squareEnv: process.env.SQUARE_ENVIRONMENT
      })

      // Create card payment method with AVS configuration for sandbox
      const cardOptions = {
        style: {
          '.input-container': {
            borderColor: '#e0e0e0',
            borderRadius: '8px'
          },
          '.input-container.is-focus': {
            borderColor: '#4ECDC4'
          },
          '.input-container.is-error': {
            borderColor: '#dc3545'
          },
          '.message-text': {
            color: '#dc3545'
          }
        }
      }

      // For sandbox environment, enable billing address collection
      // Note: We use our own billing address form instead of Square's includeInputLabels
      // to avoid duplicate card input fields and have better control over the UI
      if (isSandbox) {
        setShowBillingAddress(true)
        console.log('Sandbox mode: Enabling billing address collection for AVS compatibility')
        console.log('Using custom billing address form (not Square includeInputLabels to avoid duplication)')
      }

      const card = await payments.card(cardOptions)

      console.log('Square card object created, attempting to attach...')

      // Safe container preparation to prevent duplicates
      if (containerElement.children.length > 0) {
        console.log('Safely clearing existing content from container to prevent duplication')

        // Use safe removal method for existing children
        const existingChildren = Array.from(containerElement.children)
        existingChildren.forEach((child, index) => {
          try {
            if (child.parentNode === containerElement) {
              containerElement.removeChild(child)
              console.log(`✅ Safely removed existing child ${index + 1}/${existingChildren.length}`)
            }
          } catch (removeError) {
            console.warn(`⚠️ Could not remove existing child ${index + 1}:`, removeError)
            // Try alternative removal
            try {
              child.remove()
            } catch (altError) {
              console.warn('⚠️ Alternative removal failed:', altError)
            }
          }
        })
      }

      // Attach card form to container using the found element
      await card.attach(containerElement)

      console.log('Square card form attached successfully!')
      setPaymentForm(card)
      setIsLoading(false)
      setErrorMessage('')

    } catch (error) {
      console.error('Error initializing Square payment form:', error)

      // Only update state if component is still mounted
      if (mountedRef.current) {
        setIsLoading(false)

        // Provide user-friendly error messages
        let userMessage = 'Failed to initialize payment form'

        if (error.message.includes('container not available')) {
          userMessage = 'Payment form container not ready. Please try again.'
        } else if (error.message.includes('Square configuration')) {
          userMessage = 'Payment system configuration error. Please contact support.'
        } else if (error.message.includes('InvalidStylesError')) {
          userMessage = 'Payment form styling error. Retrying with default styles...'
          // Attempt retry with no custom styles
          setTimeout(() => {
            if (mountedRef.current) {
              setInitializationAttempted(false)
              loadSquareSDK()
            }
          }, 1000)
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          userMessage = 'Network error. Please check your connection and try again.'
        }

        setErrorMessage(userMessage)
        onError(error)
      } else {
        console.log('Component unmounted, skipping error state update')
      }
    }
  }

  const handlePayment = async () => {
    if (!paymentForm) {
      setErrorMessage('Payment form not ready')
      return
    }

    try {
      setIsProcessing(true)
      setErrorMessage('')

      console.log('🔄 Starting payment tokenization with auth protection...')

      // Enable payment operation protection
      const { startPOSPaymentOperation } = await import('@/lib/pos-auth-protection')
      startPOSPaymentOperation()

      // Prepare tokenization options with billing address for sandbox
      const tokenizeOptions = {}

      // Include billing address for sandbox environment to satisfy AVS requirements
      if (showBillingAddress) {
        tokenizeOptions.billingContact = {
          addressLines: [billingAddress.addressLine1, billingAddress.addressLine2].filter(Boolean),
          city: billingAddress.locality,
          state: billingAddress.administrativeDistrictLevel1,
          postalCode: billingAddress.postalCode,
          country: billingAddress.country
        }
        console.log('Including billing address for AVS verification:', tokenizeOptions.billingContact)
      }

      // Tokenize payment method with billing address
      const result = await paymentForm.tokenize(tokenizeOptions)

      if (result.status === 'OK') {
        console.log('✅ Payment tokenized successfully with AVS data')
        // Process payment with the token
        const paymentResponse = await processPayment(result.token)

        if (paymentResponse.success) {
          console.log('✅ Payment processed successfully')
          onSuccess({
            paymentId: paymentResponse.paymentId,
            paymentStatus: 'COMPLETED',
            paymentDetails: {
              token: result.token,
              amount: amount,
              currency: currency,
              transactionId: paymentResponse.transactionId
            }
          })
        } else {
          throw new Error(paymentResponse.error || 'Payment processing failed')
        }
      } else {
        console.error('❌ Payment tokenization failed:', result.errors)
        const errorMsg = result.errors?.[0]?.message || 'Payment tokenization failed'
        setErrorMessage(errorMsg)
        onError(new Error(errorMsg))
      }
    } catch (error) {
      console.error('Payment processing error:', error)
      setErrorMessage(error.message || 'Payment failed. Please try again.')
      onError(error)
    } finally {
      setIsProcessing(false)

      // End payment operation protection
      try {
        const { endPOSPaymentOperation } = await import('@/lib/pos-auth-protection')
        endPOSPaymentOperation()
      } catch (protectionError) {
        console.warn('Error ending POS payment protection:', protectionError)
      }
    }
  }

  const processPayment = async (token) => {
    try {
      console.log('🔄 Processing POS payment...')

      // Get authentication token from Supabase session (simple approach)
      const { supabase } = await import('@/lib/supabase')

      // Get current session without forcing refresh
      const { data: { session }, error } = await supabase.auth.getSession()

      console.log('Payment authentication check:', {
        hasSession: !!session,
        hasUser: !!session?.user,
        hasToken: !!session?.access_token,
        userEmail: session?.user?.email,
        error: error?.message
      })

      if (!session?.access_token) {
        const errorMsg = error?.message || 'No active session'
        console.error('❌ Payment authentication failed:', errorMsg)
        throw new Error(`Authentication required for payment processing. Please refresh the page and log in again. (${errorMsg})`)
      }

      console.log('✅ Payment authentication successful for user:', session.user?.email)

      const response = await fetch('/api/admin/pos/process-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          token,
          amount,
          currency,
          orderDetails
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Payment processing failed')
      }

      return data
    } catch (error) {
      console.error('Payment API error:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // Always render the container div to avoid hydration issues
  // Show loading state within the container instead of replacing it
  // Wrap in error boundary protection
  try {
    return (
    <div className={styles.squarePaymentContainer}>
      <div className={styles.paymentFormHeader}>
        <h4>Card Payment</h4>
        <div className={styles.paymentAmount}>
          Amount: <span>${parseFloat(amount || 0).toFixed(2)} {currency}</span>
        </div>
      </div>

      {errorMessage && (
        <div className={styles.paymentError}>
          <span className={styles.errorIcon}>⚠️</span>
          <div className={styles.errorContent}>
            <div className={styles.errorText}>{errorMessage}</div>
            <button
              onClick={() => {
                console.log('🔄 Retry button clicked - performing safe cleanup before retry')
                setErrorMessage('')
                setInitializationAttempted(false)
                setIsLoading(true)

                // Perform safe cleanup before retry
                safeCleanupSquareForm()

                // Wait for cleanup to complete before retrying
                setTimeout(() => {
                  if (mountedRef.current) {
                    loadSquareSDK()
                  }
                }, 200)
              }}
              className={styles.retryButton}
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <div className={styles.cardFormContainer}>
        {/* Always render the container div to prevent hydration issues */}
        <div
          id="pos-square-card-container"
          ref={containerRef}
          className={styles.cardForm}
          data-testid="square-card-container"
        >
          {/* Square card form will be injected here */}
          {isLoading && (
            <div className={styles.cardFormPlaceholder}>
              <div className={styles.loadingSpinner}></div>
              <p>Initializing secure payment form...</p>
            </div>
          )}
        </div>

        {/* Billing Address Form for Sandbox AVS Verification */}
        {showBillingAddress && !isLoading && (
          <div className={styles.billingAddressForm}>
            <h5>Billing Address (Required for Test Environment)</h5>
            <div className={styles.addressGrid}>
              <div className={styles.addressField}>
                <label>Address Line 1</label>
                <input
                  type="text"
                  value={billingAddress.addressLine1}
                  onChange={(e) => setBillingAddress(prev => ({...prev, addressLine1: e.target.value}))}
                  placeholder="1455 Market St"
                />
              </div>
              <div className={styles.addressField}>
                <label>Address Line 2</label>
                <input
                  type="text"
                  value={billingAddress.addressLine2}
                  onChange={(e) => setBillingAddress(prev => ({...prev, addressLine2: e.target.value}))}
                  placeholder="Suite 600"
                />
              </div>
              <div className={styles.addressField}>
                <label>City</label>
                <input
                  type="text"
                  value={billingAddress.locality}
                  onChange={(e) => setBillingAddress(prev => ({...prev, locality: e.target.value}))}
                  placeholder="San Francisco"
                />
              </div>
              <div className={styles.addressField}>
                <label>State</label>
                <input
                  type="text"
                  value={billingAddress.administrativeDistrictLevel1}
                  onChange={(e) => setBillingAddress(prev => ({...prev, administrativeDistrictLevel1: e.target.value}))}
                  placeholder="CA"
                />
              </div>
              <div className={styles.addressField}>
                <label>ZIP Code</label>
                <input
                  type="text"
                  value={billingAddress.postalCode}
                  onChange={(e) => setBillingAddress(prev => ({...prev, postalCode: e.target.value}))}
                  placeholder="94103"
                />
              </div>
              <div className={styles.addressField}>
                <label>Country</label>
                <select
                  value={billingAddress.country}
                  onChange={(e) => setBillingAddress(prev => ({...prev, country: e.target.value}))}
                >
                  <option value="US">United States</option>
                  <option value="AU">Australia</option>
                  <option value="CA">Canada</option>
                  <option value="GB">United Kingdom</option>
                </select>
              </div>
            </div>
            <div className={styles.addressNote}>
              <p><strong>Note:</strong> This billing address is required for Square's sandbox environment to pass Address Verification System (AVS) checks. Use the pre-filled test address or enter a valid address.</p>
            </div>
          </div>
        )}
      </div>

      <div className={styles.paymentActions}>
        <button
          onClick={handlePayment}
          disabled={isProcessing || !paymentForm}
          className={styles.processPaymentButton}
        >
          {isProcessing ? (
            <>
              <div className={styles.buttonSpinner}></div>
              Processing...
            </>
          ) : (
            `Charge $${parseFloat(amount || 0).toFixed(2)}`
          )}
        </button>
      </div>

      <div className={styles.paymentSecurity}>
        <div className={styles.securityBadges}>
          <span className={styles.securityBadge}>🔒 SSL Encrypted</span>
          <span className={styles.securityBadge}>✅ PCI Compliant</span>
          <span className={styles.securityBadge}>🛡️ Square Secure</span>
        </div>
        <p className={styles.securityText}>
          Your payment information is processed securely by Square and never stored on our servers.
        </p>
      </div>
    </div>
    )
  } catch (renderError) {
    console.error('❌ POSSquarePayment render error:', renderError)

    // Perform emergency cleanup
    try {
      safeCleanupSquareForm()
    } catch (cleanupError) {
      console.error('❌ Emergency cleanup failed:', cleanupError)
    }

    // Return error fallback UI
    return (
      <div className={styles.squarePaymentContainer}>
        <div className={styles.paymentError}>
          <span className={styles.errorIcon}>⚠️</span>
          <div className={styles.errorContent}>
            <div className={styles.errorText}>
              Payment form encountered an error. Please refresh the page and try again.
            </div>
            <button
              onClick={() => {
                console.log('🔄 Error fallback refresh clicked')
                window.location.reload()
              }}
              className={styles.retryButton}
            >
              Refresh Page
            </button>
          </div>
        </div>
      </div>
    )
  }
}
