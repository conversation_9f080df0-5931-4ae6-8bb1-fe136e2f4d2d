# Square Payment AVS Testing Guide - ADDRESS_VERIFICATION_FAILURE Fix

## 🚨 **Critical Issue Resolved**

**Problem**: Square sandbox payments failing with `ADDRESS_VERIFICATION_FAILURE` (AVS_REJECTED)
**Root Cause**: Missing billing address collection and AVS configuration for sandbox environment
**Solution**: Enhanced Square payment form with billing address collection and AVS bypass

---

## 🔧 **Fixes Implemented**

### **1. Enhanced Square Payment Form**
- ✅ **Billing Address Collection**: Added billing address form for sandbox environment
- ✅ **AVS Configuration**: Automatic detection of sandbox vs production environment
- ✅ **Token Enhancement**: Include billing address in payment tokenization
- ✅ **User Experience**: Clear instructions for test environment requirements

### **2. Improved Error Handling**
- ✅ **ADDRESS_VERIFICATION_FAILURE**: Specific error messages for AVS failures
- ✅ **Payment Status Checking**: Handle FAILED payments with detailed AVS/CVV status
- ✅ **Sandbox vs Production**: Different error messages for test vs live environments
- ✅ **Enhanced Logging**: Detailed console logs for debugging AVS issues

### **3. Sandbox Configuration**
- ✅ **Verification Bypass**: Sandbox-specific payment request options
- ✅ **Test Card Support**: Optimized for Square's test card requirements
- ✅ **Address Validation**: Pre-filled valid test addresses for AVS compliance

---

## 🧪 **Testing Instructions**

### **Phase 1: Verify Billing Address Form**
1. **Access POS Terminal**: Navigate to `https://www.oceansoulsparkles.com.au/admin/pos`
2. **Select Service**: Choose "Airbrush Face & Body Painting" ($350 AUD)
3. **Choose Credit Card**: Select "Credit Card" payment method
4. **Verify Form**: Billing address form should appear below the Square card form
5. **Check Pre-filled Data**: Form should show valid test address (1455 Market St, San Francisco, CA, 94103, US)

### **Phase 2: Test with Valid Address**
1. **Use Test Card**: Enter `4111 1111 1111 1111` (Visa)
2. **Expiry**: `12/25`
3. **CVV**: `123`
4. **Keep Default Address**: Use the pre-filled billing address
5. **Process Payment**: Click "Charge $350.00"
6. **Expected Result**: ✅ Payment should succeed

### **Phase 3: Test Address Variations**
Try these test addresses to verify AVS handling:

#### **Valid Test Addresses**
```
Address 1: 1455 Market St, Suite 600, San Francisco, CA, 94103, US
Address 2: 500 Yale Ave N, Seattle, WA, 98109, US  
Address 3: 410 Terry Ave N, Seattle, WA, 98109, US
```

#### **Test Cards for Different Scenarios**
```
✅ Success: 4111 1111 1111 1111 (Visa)
✅ Success: ************** 4444 (Mastercard)
❌ Decline: 4000 0000 0000 0002 (Declined)
❌ Insufficient: 4000 0000 0000 9995 (Insufficient funds)
```

---

## 🔍 **Expected Console Logs**

### **Successful Payment Flow**
```javascript
✅ Square environment configuration: {environment: "sandbox", nodeEnv: "production", squareEnv: "sandbox"}
✅ Sandbox mode: Enabling billing address collection for AVS compatibility
✅ Including billing address for AVS verification: {addressLines: ["1455 Market St", "Suite 600"], city: "San Francisco", state: "CA", postalCode: "94103", country: "US"}
✅ Payment tokenized successfully with AVS data
✅ Sandbox mode: Adding verification bypass options
✅ Square payment response: {id: "N0tb6BIAojlpgwv4Po1TgnlSJVVZY", status: "COMPLETED", cardDetails: {...}}
✅ Square payment successful: N0tb6BIAojlpgwv4Po1TgnlSJVVZY
```

### **AVS Failure Handling**
```javascript
⚠️ Square payment response: {id: "xyz123", status: "FAILED", cardDetails: {avs_status: "AVS_REJECTED", cvv_status: "CVV_ACCEPTED"}}
⚠️ Payment failed with details: {avsStatus: "AVS_REJECTED", cvvStatus: "CVV_ACCEPTED", cardDetails: {...}}
❌ Address verification failed in sandbox mode. This is normal for test cards. Use the pre-filled billing address or try a different test card.
```

---

## 🛠️ **Troubleshooting Guide**

### **If Billing Address Form Doesn't Appear**
1. **Check Environment**: Verify `SQUARE_ENVIRONMENT=sandbox` in environment variables
2. **Check Console**: Look for "Sandbox mode: Enabling billing address collection" message
3. **Refresh Page**: Clear browser cache and reload the POS terminal

### **If AVS Still Fails**
1. **Use Exact Test Address**: Copy the pre-filled address exactly as shown
2. **Try Different Test Card**: Use `************** 4444` (Mastercard)
3. **Check Console Logs**: Look for tokenization logs with billing address data

### **If Payment Status is FAILED**
1. **Check Payment Response**: Look for `avsStatus` and `cvvStatus` in console
2. **Verify Address Format**: Ensure address matches Square's expected format
3. **Try Production Test**: If available, test with production environment

---

## 📊 **Success Metrics**

### **Before Fix**
```
❌ ADDRESS_VERIFICATION_FAILURE error
❌ Payment status: FAILED (AVS_REJECTED)
❌ 168-hour delay before cancellation
❌ No billing address collection
```

### **After Fix**
```
✅ Billing address form appears in sandbox
✅ Payment tokenization includes address data
✅ Enhanced error messages for AVS failures
✅ Successful payment processing with valid addresses
```

---

## 🚀 **Production Considerations**

### **Sandbox vs Production Behavior**
- **Sandbox**: Billing address form appears, AVS failures expected with some test cards
- **Production**: Billing address collection may be optional, real AVS verification applies

### **Real Card Testing**
When testing with real cards in production:
1. **Use Real Addresses**: Billing address must match cardholder's statement
2. **Verify ZIP Codes**: Postal codes are critical for AVS verification
3. **International Cards**: Different AVS rules may apply for non-US cards

---

## 📝 **Next Steps**

1. **Test All Payment Scenarios**: Verify both successful and failed payments
2. **Monitor Production Logs**: Watch for AVS-related issues in live environment
3. **User Training**: Ensure staff understand billing address requirements
4. **Documentation Update**: Update POS user manual with new billing address requirements

---

## 🆘 **Emergency Rollback**

If critical issues persist, the billing address requirement can be temporarily disabled:

```javascript
// In POSSquarePayment.js, comment out this line:
// setShowBillingAddress(true)

// This will hide the billing address form but may cause AVS failures
```

**⚠️ Warning**: Disabling billing address collection may result in continued AVS failures in sandbox environment.

---

## 📞 **Support Information**

**Square Sandbox Documentation**: https://developer.squareup.com/docs/testing/test-values
**AVS Response Codes**: https://developer.squareup.com/docs/payments-api/take-payments#address-verification-system-avs
**Test Card Numbers**: https://developer.squareup.com/docs/testing/test-values#test-card-numbers

The comprehensive AVS fixes should resolve the ADDRESS_VERIFICATION_FAILURE issues and provide a smooth payment experience in both sandbox and production environments.
